<Window x:Class="TeklaTool.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:TeklaTool"
        xmlns:views="clr-namespace:TeklaTool.Views"
        xmlns:viewModels="clr-namespace:TeklaTool.ViewModels"
        mc:Ignorable="d"
        Title="TeklaList"
        Height="700"
        Width="1200"
        Topmost="{Binding IsTopMost}">

    <Window.DataContext>
        <viewModels:MainViewModel />
    </Window.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <DockPanel Grid.Row="0" Background="#F0F0F0" Height="32" LastChildFill="False">
            <StackPanel Orientation="Horizontal" DockPanel.Dock="Left" Margin="2,0">
            <!-- 数据加载组 -->
            <Button Command="{Binding LoadAllPartsCommand}" Style="{StaticResource ToolBarButtonStyle}" Content="加载所有零件"/>
            <Button Command="{Binding LoadSelectedPartsCommand}" Style="{StaticResource ToolBarButtonStyle}" Content="加载选中零件"/>
            <Button Command="{Binding RefreshDataCommand}" Style="{StaticResource ToolBarButtonStyle}" ToolTip="清除缓存并重新加载数据">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🔄" FontSize="14" Margin="0,0,2,0"/>
                    <TextBlock Text="刷新数据"/>
                </StackPanel>
            </Button>

            <!-- 分隔符 -->
            <Border Width="1" Height="20" Background="#C0C0C0" Margin="4,0" VerticalAlignment="Center"/>

            <!-- 视图模式组 -->
            <Button Command="{Binding ToggleAssemblyModeCommand}" Style="{StaticResource ToolBarButtonStyle}" Content="{Binding IsAssemblyMode, Converter={StaticResource BoolToModeTextConverter}}"/>

            <!-- 分隔符 -->
            <Border Width="1" Height="20" Background="#C0C0C0" Margin="4,0" VerticalAlignment="Center"/>

            <!-- 搜索组 -->
            <TextBox Width="90" Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" Margin="1,0" VerticalAlignment="Center" Padding="2"/>
            <Button Command="{Binding SearchCommand}" Style="{StaticResource ToolBarButtonStyle}" Content="搜索"/>

            <!-- 分隔符 -->
            <Border Width="1" Height="20" Background="#C0C0C0" Margin="4,0" VerticalAlignment="Center"/>

            <!-- 筛选组 -->
            <Button x:Name="ClearAllFiltersButton" Style="{StaticResource ToolBarButtonStyle}" Click="ClearAllFiltersButton_Click" ToolTip="清除所有已启用的筛选项">
                <StackPanel Orientation="Horizontal">
                    <Path Width="12" Height="12" Stretch="Uniform"
                          Data="M8,2 L14,2 L14,3 L13,4 L9,8 L9,14 L8,16 L7,16 L6,14 L6,8 L2,4 L1,3 L1,2 L8,2 Z"
                          Fill="Red"
                          Stroke="Gray"
                          StrokeThickness="0.5"
                          Margin="0,0,2,0"/>
                    <TextBlock Text="清除筛选" FontSize="12" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            <CheckBox Content="组合相同行" IsChecked="{Binding IsMergeRows}" Style="{StaticResource ToolBarCheckBoxStyle}" ToolTip="勾选后相同编号会合并为一行（零件和构件模式均可用）"/>

            <!-- 分隔符 -->
            <Border Width="1" Height="20" Background="#C0C0C0" Margin="4,0" VerticalAlignment="Center"/>

            <!-- 设置组 -->
            <CheckBox Content="窗口置顶" IsChecked="{Binding IsTopMost}" Style="{StaticResource ToolBarCheckBoxStyle}"/>
            <CheckBox Content="启用高亮" IsChecked="{Binding EnableHighlight}" Style="{StaticResource ToolBarCheckBoxStyle}" ToolTip="启用/禁用选择列表时高亮显示模型功能"/>
            <CheckBox Content="日志" IsChecked="{Binding EnableLogging}" Style="{StaticResource ToolBarCheckBoxStyle}" ToolTip="启用/禁用日志记录功能，关闭可减少性能开销"/>
            </StackPanel>
        </DockPanel>

        <!-- 数据网格 -->
        <Grid Grid.Row="1">
            <!-- 数据网格 -->
            <views:FilterableDataGrid x:Name="PartsDataGrid2"
                                     ItemsSource="{Binding PartsView, UpdateSourceTrigger=PropertyChanged}"
                                     Visibility="{Binding IsAssemblyMode, Converter={StaticResource BoolToInverseVisibilityConverter}}"/>

            <views:FilterableDataGrid x:Name="AssembliesDataGrid2"
                                     ItemsSource="{Binding AssembliesView, UpdateSourceTrigger=PropertyChanged}"
                                     Visibility="{Binding IsAssemblyMode, Converter={StaticResource BoolToVisibilityConverter}}"/>

            <!-- 加载指示器覆盖层 -->
            <Grid Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}"
                  Background="#80FFFFFF" Panel.ZIndex="1000">
                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                    <TextBlock Text="正在加载数据..." FontSize="16" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <ProgressBar IsIndeterminate="True" Width="200" Height="15"/>
                </StackPanel>
            </Grid>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusText}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding CountText}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding TimeText}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
