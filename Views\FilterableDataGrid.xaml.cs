using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Media;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using System.Windows.Input;
using System.Windows.Threading;

namespace TeklaTool.Views
{
    public partial class FilterableDataGrid : UserControl
    {
        // 依赖属性
        public static readonly DependencyProperty ItemsSourceProperty =
            DependencyProperty.Register("ItemsSource", typeof(IEnumerable), typeof(FilterableDataGrid),
                new PropertyMetadata(null, OnItemsSourceChanged));

        // 属性
        public IEnumerable ItemsSource
        {
            get { return (IEnumerable)GetValue(ItemsSourceProperty); }
            set { SetValue(ItemsSourceProperty, value); }
        }

        // 内部字段
        private Dictionary<string, List<string>> _columnFilters = new Dictionary<string, List<string>>();
        private ICollectionView _collectionView;
        private Dictionary<string, FilterControl> _filterControls = new Dictionary<string, FilterControl>();


        public FilterableDataGrid()
        {
            InitializeComponent();

            // 监听布局更新完成事件
            Loaded += (s, e) =>
            {
                try
                {
                    // 为所有列标题添加点击事件
                    UpdateColumnHeadersClickEvent();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 初始化列标题点击事件时出错: {ex.Message}");
                }
            };
        }

        // 当ItemsSource变更时
        private static void OnItemsSourceChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var control = (FilterableDataGrid)d;
            control.UpdateItemsSource();
        }

        // 更新数据源
        private void UpdateItemsSource()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[FilterableDataGrid] 更新数据源");

                MainDataGrid.ItemsSource = ItemsSource;

                if (ItemsSource != null)
                {
                    // 获取集合视图
                    _collectionView = CollectionViewSource.GetDefaultView(ItemsSource);
                    _collectionView.Filter = ApplyFilter;

                    // 清除现有的筛选
                    _columnFilters.Clear();

                    // 输出调试信息
                    int itemCount = 0;
                    foreach (var item in ItemsSource)
                    {
                        itemCount++;
                    }
                    System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 数据源更新完成，项数: {itemCount}");

                    // 注意：不再在此处更新所有筛选控件，改为按需在用户点击筛选按钮时更新
                    // 这可以显著提高性能和减少初始加载时的资源占用
                    System.Diagnostics.Debug.WriteLine("[FilterableDataGrid] 筛选控件将在用户点击筛选按钮时更新");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[FilterableDataGrid] 数据源为空");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 更新数据源时出错: {ex.Message}");
            }
        }

        // 更新所有筛选控件
        private void UpdateAllFilterControls()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[FilterableDataGrid] 更新所有筛选控件");

                foreach (var columnName in _filterControls.Keys.ToList())
                {
                    try
                    {
                        if (_filterControls.TryGetValue(columnName, out var filterControl))
                        {
                            var distinctValues = GetDistinctColumnValues(columnName);
                            filterControl.SetItems(distinctValues);
                        }
                    }
                    catch (Exception columnEx)
                    {
                        // 单个列更新失败不应该影响其他列
                        System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 更新列 {columnName} 的筛选控件时出错: {columnEx.Message}");

                        // 尝试设置一个空列表，以便用户知道这个筛选器有问题
                        if (_filterControls.TryGetValue(columnName, out var errorFilterControl))
                        {
                            errorFilterControl.SetItems(new List<string>());
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine("[FilterableDataGrid] 所有筛选控件更新完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 更新筛选控件时出错: {ex.Message}");
            }
        }

        // 获取列的唯一值
        private List<string> GetDistinctColumnValues(string propertyName)
        {
            var result = new List<string>();

            if (ItemsSource == null)
                return result;

            try
            {
                System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 获取列 {propertyName} 的唯一值");

                // 获取属性信息
                var itemType = GetItemType();
                if (itemType == null)
                {
                    System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 无法获取项目类型");
                    return result;
                }

                var propertyInfo = itemType.GetProperty(propertyName);
                if (propertyInfo == null)
                {
                    // 尝试使用不区分大小写的方式查找属性
                    foreach (var prop in itemType.GetProperties())
                    {
                        if (string.Equals(prop.Name, propertyName, StringComparison.OrdinalIgnoreCase))
                        {
                            propertyInfo = prop;
                            break;
                        }
                    }

                    if (propertyInfo == null)
                    {
                        System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 找不到属性: {propertyName}");
                        return result;
                    }
                }

                // 使用并发集合以提高性能
                var uniqueValues = new System.Collections.Concurrent.ConcurrentBag<string>();

                // 使用Parallel.ForEach提高大数据集的处理性能
                var itemsList = ItemsSource.Cast<object>().ToList();
                System.Threading.Tasks.Parallel.ForEach(itemsList, item =>
                {
                    if (item == null)
                        return;

                    try
                    {
                        var value = propertyInfo.GetValue(item)?.ToString() ?? string.Empty;
                        uniqueValues.Add(value);
                    }
                    catch (Exception ex)
                    {
                        // 记录错误但继续处理
                        System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 获取值时出错: {ex.Message}");
                    }
                });

                // 转换为排序的列表（去重）
                result = uniqueValues.Distinct().OrderBy(v => v).ToList();

                // 确保至少有一个空值选项
                if (result.Count == 0)
                {
                    result.Add(string.Empty);
                }

                System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 列 {propertyName} 的唯一值数量: {result.Count}");
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 获取列唯一值时出错: {ex.Message}");
                return new List<string> { string.Empty };
            }
        }

        // 获取项类型
        private Type GetItemType()
        {
            if (ItemsSource == null)
                return null;

            try
            {
                // 尝试获取第一个项的类型
                foreach (var item in ItemsSource)
                {
                    if (item != null)
                    {
                        return item.GetType();
                    }
                }

                // 如果没有项，尝试从接口获取类型
                var enumerableType = ItemsSource.GetType();
                var itemType = enumerableType.GetInterfaces()
                    .Where(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IEnumerable<>))
                    .Select(i => i.GetGenericArguments()[0])
                    .FirstOrDefault();

                return itemType;
            }
            catch
            {
                return null;
            }
        }

        // 应用筛选
        private bool ApplyFilter(object item)
        {
            // 如果没有筛选条件，显示所有项
            if (_columnFilters.Count == 0)
                return true;

            if (item == null)
                return false;

            // 检查每个筛选条件
            foreach (var filter in _columnFilters)
            {
                // 如果筛选列表为空，表示没有选中任何项，不显示任何数据
                if (filter.Value.Count == 0)
                    return false;

                try
                {
                    // 获取属性信息
                    var propertyInfo = item.GetType().GetProperty(filter.Key);
                    if (propertyInfo == null)
                        continue;

                    // 获取属性值
                    var value = propertyInfo.GetValue(item)?.ToString() ?? string.Empty;

                    // 如果属性值不在筛选列表中，则不显示该项
                    if (!filter.Value.Contains(value))
                        return false;
                }
                catch
                {
                    // 忽略获取属性值时的异常
                    continue;
                }
            }

            // 通过所有筛选条件，显示该项
            return true;
        }

        // 添加列
        public void AddColumn(string header, string bindingPath)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 添加列: {header}, {bindingPath}");

                // 创建列
                var column = new DataGridTemplateColumn();

                // 创建带有筛选控件的标题
                var headerPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal
                };

                var headerText = new TextBlock
                {
                    Text = header,
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(0, 0, 3, 0)
                };
                headerPanel.Children.Add(headerText);

                // 创建筛选控件
                var filterControl = new FilterControl
                {
                    ColumnName = bindingPath,
                    Width = 16,
                    Height = 16,
                    Margin = new Thickness(0, 0, 0, 0),
                    VerticalAlignment = VerticalAlignment.Center
                };

                // 处理筛选按钮点击事件
                filterControl.FilterButton_PreClick += (s, e) =>
                {
                    try
                    {
                        // 获取当前列的唯一值
                        var distinctValues = GetDistinctColumnValues(bindingPath);

                        // 更新筛选控件的数据
                        filterControl.SetItems(distinctValues);

                        System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 筛选按钮点击前更新: {bindingPath}, 值数量: {distinctValues.Count}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 筛选按钮点击前更新时出错: {ex.Message}");
                    }
                };

                // 处理筛选事件
                filterControl.FilterChanged += (s, e) =>
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 筛选变更: {e.ColumnName}, 选中项数: {e.SelectedValues.Count}");

                        // 更新筛选条件
                        _columnFilters[e.ColumnName] = e.SelectedValues;

                        // 刷新视图
                        if (_collectionView != null)
                        {
                            _collectionView.Refresh();
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 处理筛选变更时出错: {ex.Message}");
                    }
                };

                // 保存筛选控件引用
                _filterControls[bindingPath] = filterControl;

                headerPanel.Children.Add(filterControl);
                column.Header = headerPanel;

                // 设置单元格模板
                var template = new DataTemplate();
                var textBlock = new FrameworkElementFactory(typeof(TextBlock));
                textBlock.SetBinding(TextBlock.TextProperty, new Binding(bindingPath));
                template.VisualTree = textBlock;
                column.CellTemplate = template;

                // 添加列到数据网格
                MainDataGrid.Columns.Add(column);

                // 列添加后，尝试更新列标题点击事件
                try
                {
                    // 等待布局更新
                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        try
                        {
                            int columnIndex = MainDataGrid.Columns.Count - 1;
                            var columnHeader = GetColumnHeader(columnIndex);
                            if (columnHeader != null)
                            {
                                // 使用Tag存储绑定路径
                                columnHeader.Tag = bindingPath;

                                // 添加点击事件处理
                                columnHeader.MouseLeftButtonDown += ColumnHeader_MouseLeftButtonDown;
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 更新列标题点击事件时出错: {ex.Message}");
                        }
                    }), System.Windows.Threading.DispatcherPriority.Loaded);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 设置列标题点击事件时出错: {ex.Message}");
                }

                System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 列添加完成: {header}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 添加列时出错: {ex.Message}");
            }
        }

        // 获取列标题元素
        private DataGridColumnHeader GetColumnHeader(int columnIndex)
        {
            if (columnIndex < 0 || columnIndex >= MainDataGrid.Columns.Count)
                return null;

            // 等待布局完成
            MainDataGrid.UpdateLayout();

            // 查找列标题元素
            var presenter = FindVisualChild<DataGridColumnHeadersPresenter>(MainDataGrid);
            if (presenter != null)
            {
                var header = presenter.ItemContainerGenerator.ContainerFromIndex(columnIndex) as DataGridColumnHeader;
                return header;
            }

            return null;
        }

        // 查找视觉树中的子元素
        private T FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is T typedChild)
                    return typedChild;

                var result = FindVisualChild<T>(child);
                if (result != null)
                    return result;
            }

            return null;
        }

        // 处理列标题点击事件
        private void HandleColumnHeaderClick(string propertyName)
        {
            try
            {
                if (_collectionView == null)
                    return;

                // 获取当前排序方向
                ListSortDirection direction = ListSortDirection.Ascending;

                // 检查当前排序状态
                if (_collectionView.SortDescriptions.Count > 0)
                {
                    var currentSort = _collectionView.SortDescriptions[0];
                    if (currentSort.PropertyName == propertyName)
                    {
                        // 切换排序方向
                        direction = currentSort.Direction == ListSortDirection.Ascending ?
                            ListSortDirection.Descending : ListSortDirection.Ascending;
                    }
                }
                else if (_collectionView is ListCollectionView lcv && lcv.CustomSort != null)
                {
                    // 如果当前使用的是自定义排序器
                    if (lcv.CustomSort is NaturalStringComparer nsc && nsc.PropertyName == propertyName)
                    {
                        direction = ListSortDirection.Descending;
                    }
                    else if (lcv.CustomSort is ReverseComparer rc &&
                             rc.Inner is NaturalStringComparer innerNsc &&
                             innerNsc.PropertyName == propertyName)
                    {
                        direction = ListSortDirection.Ascending;
                    }
                }

                // 应用排序
                SetDefaultSort(propertyName, direction);

                System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 列标题点击排序: {propertyName}, {direction}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 处理列标题点击时出错: {ex.Message}");
            }
        }

        // 自然排序比较器
        public class NaturalStringComparer : IComparer
        {
            private readonly string _propertyName;
            public string PropertyName => _propertyName;

            public NaturalStringComparer(string propertyName)
            {
                _propertyName = propertyName;
            }

            public int Compare(object x, object y)
            {
                string sx = GetPropertyString(x);
                string sy = GetPropertyString(y);
                return NaturalCompare(sx, sy);
            }

            private string GetPropertyString(object obj)
            {
                if (obj == null) return string.Empty;
                var prop = obj.GetType().GetProperty(_propertyName);
                if (prop == null) return string.Empty;
                return prop.GetValue(obj)?.ToString() ?? string.Empty;
            }

            // 核心自然排序算法
            private int NaturalCompare(string a, string b)
            {
                if (a == b) return 0;

                // 处理空值
                if (string.IsNullOrEmpty(a) && string.IsNullOrEmpty(b)) return 0;
                if (string.IsNullOrEmpty(a)) return -1;
                if (string.IsNullOrEmpty(b)) return 1;

                // 如果两个都是纯数字，直接按数字比较
                if (double.TryParse(a, out double numA) && double.TryParse(b, out double numB))
                {
                    return numA.CompareTo(numB);
                }

                // 使用正则表达式分割数字和非数字部分
                var regex = new Regex("(\\d+|\\D+)");
                var ma = regex.Matches(a);
                var mb = regex.Matches(b);
                int count = Math.Min(ma.Count, mb.Count);

                for (int i = 0; i < count; i++)
                {
                    string ca = ma[i].Value;
                    string cb = mb[i].Value;

                    // 如果都是数字，按数字比较
                    if (long.TryParse(ca, out long na) && long.TryParse(cb, out long nb))
                    {
                        int cmp = na.CompareTo(nb);
                        if (cmp != 0) return cmp;
                    }
                    else
                    {
                        // 按字符串比较（忽略大小写）
                        int cmp = string.Compare(ca, cb, StringComparison.OrdinalIgnoreCase);
                        if (cmp != 0) return cmp;
                    }
                }

                // 如果前面的部分都相同，比较长度
                return ma.Count.CompareTo(mb.Count);
            }
        }

        // 反向比较器
        public class ReverseComparer : IComparer
        {
            private readonly IComparer _inner;
            public IComparer Inner => _inner;

            public ReverseComparer(IComparer inner) { _inner = inner; }
            public int Compare(object x, object y) => _inner.Compare(y, x);
        }

        // 设置默认排序
        public void SetDefaultSort(string propertyName, ListSortDirection direction = ListSortDirection.Ascending)
        {
            try
            {
                if (_collectionView != null)
                {
                    // 清除现有的排序描述
                    _collectionView.SortDescriptions.Clear();

                    // 判断是否为自然排序字段（包含数字的字符串字段）
                    if (IsNaturalSortField(propertyName))
                    {
                        if (_collectionView is ListCollectionView lcv)
                        {
                            if (direction == ListSortDirection.Ascending)
                                lcv.CustomSort = new NaturalStringComparer(propertyName);
                            else
                                lcv.CustomSort = new ReverseComparer(new NaturalStringComparer(propertyName));
                        }
                    }
                    else
                    {
                        // 添加新的排序描述
                        _collectionView.SortDescriptions.Add(new SortDescription(propertyName, direction));
                    }

                    // 刷新视图
                    _collectionView.Refresh();

                    System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 设置默认排序: {propertyName}, {direction}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 设置默认排序时出错: {ex.Message}");
            }
        }

        // 判断是否为需要自然排序的字段
        private bool IsNaturalSortField(string propertyName)
        {
            // 这些字段通常包含数字，需要自然排序
            var naturalSortFields = new HashSet<string>
            {
                "PartNumber",           // 零件编号 (如: P1, P2, P10, P20)
                "AssemblyNumber",       // 构件编号 (如: A1, A2, A10, A20)
                "Index",                // 序号 (如: 1, 2, 10, 20)
                "Count",                // 数量 (数字)
                "BoltCount",            // 螺栓数 (数字)
                "PartCount",            // 零件数 (数字)
                "AssemblyStartNumber",  // 构件起始号 (数字)
                "PartStartNumber",      // 零件起始号 (数字)
                "AssemblyPrefix",       // 构件前缀 (可能包含数字)
                "PartPrefix"            // 零件前缀 (可能包含数字)
            };

            return naturalSortFields.Contains(propertyName);
        }

        // 清除所有筛选
        public void ClearFilters()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[FilterableDataGrid] 清除所有筛选");

                // 清除筛选条件
            _columnFilters.Clear();

                // 更新所有筛选控件的状态
                foreach (var filterControl in _filterControls.Values)
                {
                    filterControl.IsFiltered = false;
                }

                // 刷新视图
            if (_collectionView != null)
            {
                _collectionView.Refresh();
                }

                System.Diagnostics.Debug.WriteLine("[FilterableDataGrid] 所有筛选已清除");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[FilterableDataGrid] 清除所有筛选时出错: {ex.Message}");
            }
        }

        // 更新所有列标题的点击事件
        private void UpdateColumnHeadersClickEvent()
        {
            for (int i = 0; i < MainDataGrid.Columns.Count; i++)
            {
                var column = MainDataGrid.Columns[i];
                var bindingPath = GetColumnBindingPath(column);
                if (string.IsNullOrEmpty(bindingPath))
                    continue;

                var columnHeader = GetColumnHeader(i);
                if (columnHeader != null)
                {
                    // 使用Tag存储绑定路径
                    columnHeader.Tag = bindingPath;

                    // 添加点击事件处理
                    columnHeader.MouseLeftButtonDown += ColumnHeader_MouseLeftButtonDown;
                }
            }
        }

        // 列标题点击事件处理
        private void ColumnHeader_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (sender is DataGridColumnHeader header && header.Tag is string propertyName)
            {
                // 阻止事件冒泡，避免触发筛选控件的点击事件
                e.Handled = true;

                // 处理排序
                HandleColumnHeaderClick(propertyName);
            }
        }

        // 获取列的绑定路径
        private string GetColumnBindingPath(DataGridColumn column)
        {
            if (column is DataGridTemplateColumn templateColumn)
            {
                // 从列的DataContext中获取绑定路径
                // 这里我们直接返回列添加时使用的bindingPath
                foreach (var kvp in _filterControls)
                {
                    if (kvp.Value.Parent is FrameworkElement parent &&
                        parent.Parent is FrameworkElement grandParent &&
                        grandParent == column.Header)
                    {
                        return kvp.Key;
                    }
                }
            }

            return null;
        }
    }
}
