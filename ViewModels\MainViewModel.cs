using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using TeklaTool.Models;
using TeklaTool.Services;
using TeklaTool.Utils;

namespace TeklaTool.ViewModels
{
    public class MainViewModel : ViewModelBase, IDisposable
    {
        private TeklaModelService _teklaModelService;
        private PartListViewModel _partListViewModel;
        private AssemblyListViewModel _assemblyListViewModel;
        private readonly AppConfig _config = AppConfig.Instance;
        private bool _isLoading;
        private bool _isAssemblyMode;
        private string _statusText;
        private string _countText;
        private string _timeText;
        private string _searchText;
        private bool _isTopMost;
        private bool _enableHighlight;
        private bool _enableLogging = false;
        private bool _disposed = false;

        public PartListViewModel PartList => _partListViewModel;
        public AssemblyListViewModel AssemblyList => _assemblyListViewModel;

        public MainViewModel()
        {
            // 使用改进的异常处理初始化
            var success = ExceptionHandler.HandleTeklaOperation(() =>
            {
                InitializeServices();
                InitializeProperties();
                InitializeCommands();
                CheckTeklaConnection();
            }, "初始化MainViewModel");

            if (!success)
            {
                // 如果初始化失败，确保基本功能可用
                EnsureBasicFunctionality();
            }
        }

        /// <summary>
        /// 初始化服务
        /// </summary>
        private void InitializeServices()
        {
            _teklaModelService = new TeklaModelService();
            _partListViewModel = new PartListViewModel(_teklaModelService, this);
            _assemblyListViewModel = new AssemblyListViewModel(_teklaModelService, this);
        }

        /// <summary>
        /// 初始化属性
        /// </summary>
        private void InitializeProperties()
        {
            _isLoading = false;
            _isAssemblyMode = false;
            _statusText = "就绪";
            _countText = "数量: 0";
            _timeText = "时间: 0s";
            _searchText = string.Empty;
            _isTopMost = false;
            _enableHighlight = true;
            _enableLogging = false;

            // 同步日志开关状态
            Logger.IsEnabled = _enableLogging;
        }

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            LoadAllPartsCommand = new RelayCommand(async param => await LoadAllParts(true), param => !IsLoading && !_disposed);
            LoadSelectedPartsCommand = new RelayCommand(async param => await LoadSelectedParts(), param => !IsLoading && !_disposed);
            RefreshDataCommand = new RelayCommand(async param => {
                System.Diagnostics.Debug.WriteLine("RefreshDataCommand: 命令被执行");
                await RefreshData();
            }, param => {
                bool canExecute = !IsLoading && !_disposed;
                System.Diagnostics.Debug.WriteLine($"RefreshDataCommand CanExecute: {canExecute} (IsLoading: {IsLoading}, _disposed: {_disposed})");
                return canExecute;
            });
            ToggleAssemblyModeCommand = new RelayCommand(async param => await ToggleAssemblyMode(), param => !IsLoading && !_disposed);
            SearchCommand = new RelayCommand(param => Search(), param => !IsLoading && !_disposed);
            ToggleTopMostCommand = new RelayCommand(param => ToggleTopMost(), param => !_disposed);
            ToggleHighlightCommand = new RelayCommand(param => ToggleHighlight(), param => !_disposed);
            ToggleLoggingCommand = new RelayCommand(param => ToggleLogging(), param => !_disposed);
        }

        /// <summary>
        /// 检查Tekla连接状态
        /// </summary>
        private void CheckTeklaConnection()
        {
            bool isConnected = _teklaModelService?.GetConnectionStatus() ?? false;
            if (!isConnected)
            {
                _statusText = "未连接到Tekla模型，请确保Tekla Structures已启动并打开了模型";
                Logger.LogWarning("初始化时未连接到Tekla模型");
            }
            else
            {
                Logger.LogInfo("MainViewModel初始化完成，已连接到Tekla模型");
            }
        }

        /// <summary>
        /// 确保基本功能可用（初始化失败时的后备方案）
        /// </summary>
        private void EnsureBasicFunctionality()
        {
            _statusText = "初始化失败，功能受限";

            // 创建空的命令以防止空引用异常
            LoadAllPartsCommand ??= new RelayCommand(param => { }, param => false);
            LoadSelectedPartsCommand ??= new RelayCommand(param => { }, param => false);
            RefreshDataCommand ??= new RelayCommand(param => { }, param => false);
            ToggleAssemblyModeCommand ??= new RelayCommand(param => { }, param => false);
            SearchCommand ??= new RelayCommand(param => { }, param => false);
            ToggleTopMostCommand ??= new RelayCommand(param => { }, param => false);
            ToggleHighlightCommand ??= new RelayCommand(param => { }, param => false);
            ToggleLoggingCommand ??= new RelayCommand(param => { }, param => false);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public bool IsAssemblyMode
        {
            get => _isAssemblyMode;
            set => SetProperty(ref _isAssemblyMode, value);
        }

        public string StatusText
        {
            get => _statusText;
            set => SetProperty(ref _statusText, value);
        }

        public string CountText
        {
            get => _countText;
            set => SetProperty(ref _countText, value);
        }

        public string TimeText
        {
            get => _timeText;
            set => SetProperty(ref _timeText, value);
        }

        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        public bool IsTopMost
        {
            get => _isTopMost;
            set => SetProperty(ref _isTopMost, value);
        }

        public bool EnableHighlight
        {
            get => _enableHighlight;
            set => SetProperty(ref _enableHighlight, value);
        }

        public bool EnableLogging
        {
            get => _enableLogging;
            set
            {
                if (SetProperty(ref _enableLogging, value))
                {
                    // 同步更新Logger的开关状态
                    Logger.IsEnabled = value;
                    Logger.LogInfo($"日志功能已{(value ? "开启" : "关闭")}");
                }
            }
        }

        // 兼容旧绑定，后续可移除
        public IEnumerable<object> PartsView => PartList.PartsView;
        public IEnumerable<object> AssembliesView => AssemblyList.AssembliesView;
        public bool IsMergeRows
        {
            get => PartList.IsMergeRows;
            set
            {
                if (PartList.IsMergeRows != value)
                {
                    PartList.IsMergeRows = value;
                    AssemblyList.IsMergeRows = value; // 同时设置两个视图模型的合并行属性
                    OnPropertyChanged(nameof(IsMergeRows));
                    OnPropertyChanged(nameof(PartsView));
                    OnPropertyChanged(nameof(AssembliesView));

                    // 更新数量显示
                    CountText = $"数量: {GetCurrentDisplayCount():N0}";
                }
            }
        }
        public ObservableCollection<TeklaModelPart> Parts => PartList.Parts;
        public ObservableCollection<PartListViewModel.MergedPartRow> MergedParts => PartList.MergedParts;
        public ObservableCollection<AssemblyInfo> Assemblies => AssemblyList.Assemblies;
        public ObservableCollection<AssemblyListViewModel.MergedAssemblyRow> MergedAssemblies => AssemblyList.MergedAssemblies;

        // 兼容旧命令绑定，后续可迁移到子ViewModel
        public ICommand LoadAllPartsCommand { get; private set; }
        public ICommand LoadSelectedPartsCommand { get; private set; }
        public ICommand RefreshDataCommand { get; private set; }
        public ICommand ToggleAssemblyModeCommand { get; private set; }
        public ICommand SearchCommand { get; private set; }
        public ICommand ToggleTopMostCommand { get; private set; }
        public ICommand ToggleHighlightCommand { get; private set; }
        public ICommand ToggleLoggingCommand { get; private set; }

        private async Task LoadAllParts(bool useCache = true)
        {
            if (IsLoading || _disposed) return;

            try
            {
                IsLoading = true;
                StatusText = "正在加载所有零件...";

                DateTime startTime = DateTime.Now;

                if (!_teklaModelService.GetConnectionStatus())
                {
                    MessageBox.Show("未连接到Tekla模型", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    StatusText = "未连接到Tekla模型";
                    return;
                }

                // 使用缓存机制
                List<TeklaModelPart> parts = await ExceptionHandler.HandleTeklaOperation(async () =>
                {
                    return await Task.Run(() => _teklaModelService.GetAllParts(useCache));
                }, "加载所有零件");

                if (parts != null)
                {
                    if (IsAssemblyMode)
                    {
                        await ProcessAssemblies(parts);
                    }
                    else
                    {
                        PartList.SetParts(parts);
                    }

                    TimeSpan elapsed = DateTime.Now - startTime;
                    CountText = $"数量: {GetCurrentDisplayCount():N0}";
                    TimeText = $"时间: {elapsed.TotalSeconds:F1}s";
                    StatusText = "加载完成";
                    Logger.LogInfo($"成功加载 {parts.Count} 个零件");
                }
                else
                {
                    StatusText = "加载失败";
                    CountText = "数量: 0";
                    Logger.LogWarning("加载零件返回空结果");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"加载所有零件时发生未处理的错误: {ex.Message}");
                StatusText = "加载失败";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 刷新数据（清除缓存并重新加载）
        /// </summary>
        private async Task RefreshData()
        {
            if (IsLoading || _disposed) return;

            try
            {
                Logger.LogInfo("开始执行刷新数据命令");
                System.Diagnostics.Debug.WriteLine("RefreshData: 开始执行");

                IsLoading = true;
                StatusText = "正在刷新数据...";

                // 清除缓存
                ExceptionHandler.SafeExecute(() => _teklaModelService?.ClearCache(), "清除缓存");
                System.Diagnostics.Debug.WriteLine("RefreshData: 已清除缓存");

                // 重新加载数据（不使用缓存）
                await LoadAllParts(false);
                System.Diagnostics.Debug.WriteLine("RefreshData: 已重新加载数据");

                StatusText = "数据刷新完成";
                Logger.LogInfo("数据刷新完成");
            }
            catch (Exception ex)
            {
                Logger.LogError($"刷新数据时发生未处理的错误: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"RefreshData: 发生错误 - {ex.Message}");
                StatusText = "刷新失败";
            }
            finally
            {
                IsLoading = false;
                System.Diagnostics.Debug.WriteLine("RefreshData: 执行完成");
            }
        }

        private async Task LoadSelectedParts()
        {
            if (IsLoading || _disposed) return;

            try
            {
                IsLoading = true;
                StatusText = "正在加载选中零件...";

                DateTime startTime = DateTime.Now;

                if (!_teklaModelService.GetConnectionStatus())
                {
                    MessageBox.Show("未连接到Tekla模型", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    StatusText = "未连接到Tekla模型";
                    return;
                }

                List<TeklaModelPart> parts = await ExceptionHandler.HandleTeklaOperation(async () =>
                {
                    return await Task.Run(() => _teklaModelService.GetSelectedParts());
                }, "加载选中零件");

                if (parts == null || parts.Count == 0)
                {
                    MessageBox.Show("未选中任何零件", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    StatusText = "未选中任何零件";
                    Logger.LogWarning("未选中任何零件");
                    IsLoading = false;
                    return;
                }

                if (IsAssemblyMode)
                {
                    await ProcessAssemblies(parts);
                }
                else
                {
                    PartList.SetParts(parts);
                }

                TimeSpan elapsed = DateTime.Now - startTime;
                CountText = $"数量: {GetCurrentDisplayCount():N0}";
                TimeText = $"时间: {elapsed.TotalSeconds:F1}s";
                StatusText = "加载完成";
                Logger.LogInfo($"成功加载 {parts.Count} 个选中零件");
            }
            catch (Exception ex)
            {
                Logger.LogError($"加载选中零件时发生未处理的错误: {ex.Message}");
                StatusText = "加载失败";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ToggleAssemblyMode()
        {
            if (IsLoading) return;

            try
            {
                IsLoading = true;
                StatusText = "切换模式中...";

                IsAssemblyMode = !IsAssemblyMode;

                // 如果有数据，重新处理
                if (Parts.Count > 0)
                {
                    List<TeklaModelPart> parts = Parts.ToList();
                    if (IsAssemblyMode)
                    {
                        await ProcessAssemblies(parts);
                    }
                    else
                    {
                        // 切换回零件模式，不需要特殊处理
                    }
                }

                // 通知属性变更，触发MainWindow中的PropertyChanged事件处理程序
                // 这将导致重新应用默认排序
                OnPropertyChanged(nameof(IsAssemblyMode));

                // 更新数量显示
                CountText = $"数量: {GetCurrentDisplayCount():N0}";
                StatusText = $"已切换到{(IsAssemblyMode ? "构件" : "零件")}模式";
            }
            catch (Exception ex)
            {
                Logger.LogError($"切换模式时发生错误: {ex.Message}");
                MessageBox.Show($"切换模式时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText = "切换失败";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ProcessAssemblies(List<TeklaModelPart> parts)
        {
            try
            {
                StatusText = "正在处理构件数据...";

                List<AssemblyInfo> assemblies = await Task.Run(() => _teklaModelService.GetAssemblies(parts));

                // 使用 AssemblyList.SetAssemblies 方法来设置数据，这样会自动处理合并逻辑
                AssemblyList.SetAssemblies(assemblies);

                StatusText = "构件数据处理完成";
            }
            catch (Exception ex)
            {
                Logger.LogError($"处理构件数据时发生错误: {ex.Message}");
                MessageBox.Show($"处理构件数据时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText = "处理失败";
            }
        }

        private void Search()
        {
            if (IsLoading || _disposed) return;

            ExceptionHandler.SafeExecute(() =>
            {
                if (IsAssemblyMode)
                {
                    _assemblyListViewModel?.Search(SearchText);
                }
                else
                {
                    _partListViewModel?.Search(SearchText);
                }
                StatusText = string.IsNullOrEmpty(SearchText) ? "已清除搜索" : $"搜索: {SearchText}";
                Logger.LogInfo($"执行搜索: {SearchText}");
            }, "执行搜索操作");
        }

        private void ToggleTopMost()
        {
            if (_disposed) return;

            ExceptionHandler.SafeExecute(() =>
            {
                IsTopMost = !IsTopMost;
                Logger.LogInfo($"窗口置顶状态切换为: {IsTopMost}");
            }, "切换窗口置顶状态");
        }

        private void ToggleHighlight()
        {
            if (_disposed) return;

            ExceptionHandler.SafeExecute(() =>
            {
                EnableHighlight = !EnableHighlight;
                if (IsAssemblyMode && _assemblyListViewModel != null)
                {
                    _assemblyListViewModel.IsHighlighting = EnableHighlight;
                }
                Logger.LogInfo($"高亮功能切换为: {EnableHighlight}");
            }, "切换高亮功能");
        }

        private void ToggleLogging()
        {
            if (_disposed) return;

            ExceptionHandler.SafeExecute(() =>
            {
                EnableLogging = !EnableLogging;
                StatusText = $"日志功能已{(EnableLogging ? "开启" : "关闭")}";
            }, "切换日志功能");
        }

        /// <summary>
        /// 获取当前显示的数量（考虑合并行的情况）
        /// </summary>
        /// <returns>当前显示的数量</returns>
        private int GetCurrentDisplayCount()
        {
            if (IsAssemblyMode)
            {
                // 构件模式下，如果启用了合并相同行，显示合并后的数量
                return IsMergeRows ? MergedAssemblies.Count : Assemblies.Count;
            }
            else
            {
                // 零件模式下，如果启用了合并相同行，显示合并后的数量
                return IsMergeRows ? MergedParts.Count : Parts.Count;
            }
        }

        #region IDisposable Implementation

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // 释放托管资源
                    ExceptionHandler.SafeExecute(() =>
                    {
                        _teklaModelService?.Dispose();
                        _partListViewModel?.Dispose();
                        _assemblyListViewModel?.Dispose();
                        Logger.LogInfo("MainViewModel资源已释放");
                    }, "释放MainViewModel资源");
                }

                _disposed = true;
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~MainViewModel()
        {
            Dispose(false);
        }

        #endregion
    }
}
