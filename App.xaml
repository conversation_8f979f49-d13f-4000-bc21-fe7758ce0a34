<Application x:Class="TeklaTool.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:TeklaTool"
             xmlns:utils="clr-namespace:TeklaTool.Utils"
             xmlns:converters="clr-namespace:TeklaTool.Converters"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <utils:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
            <utils:BoolToInverseVisibilityConverter x:Key="BoolToInverseVisibilityConverter"/>
            <utils:BoolToModeTextConverter x:Key="BoolToModeTextConverter"/>
            <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>

            <Style TargetType="Button">
                <Setter Property="Padding" Value="8,3"/>
                <Setter Property="Margin" Value="2"/>
            </Style>

            <Style TargetType="TextBlock">
                <Setter Property="VerticalAlignment" Value="Center"/>
            </Style>

            <!-- 紧凑工具栏样式 -->
            <Style TargetType="ToolBar">
                <Setter Property="Margin" Value="0"/>
                <Setter Property="Padding" Value="2,1"/>
                <Setter Property="Background" Value="#F0F0F0"/>
                <Setter Property="BorderThickness" Value="0,0,0,1"/>
                <Setter Property="BorderBrush" Value="#D0D0D0"/>
            </Style>

            <!-- 工具栏按钮样式 -->
            <Style TargetType="Button" x:Key="ToolBarButtonStyle">
                <Setter Property="Padding" Value="6,2"/>
                <Setter Property="Margin" Value="1,0"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderBrush" Value="Transparent"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#E0E0E0"/>
                        <Setter Property="BorderBrush" Value="#C0C0C0"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- 工具栏复选框样式 -->
            <Style TargetType="CheckBox" x:Key="ToolBarCheckBoxStyle">
                <Setter Property="Margin" Value="1,0"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
                <Setter Property="Padding" Value="2,0"/>
            </Style>

            <!-- 工具栏分隔符样式 -->
            <Style TargetType="Separator" x:Key="ToolBarSeparatorStyle">
                <Setter Property="Margin" Value="3,2"/>
                <Setter Property="Width" Value="1"/>
                <Setter Property="Background" Value="#C0C0C0"/>
                <Setter Property="BorderBrush" Value="#A0A0A0"/>
                <Setter Property="BorderThickness" Value="0.5"/>
            </Style>

            <Style TargetType="DataGrid">
                <Setter Property="AutoGenerateColumns" Value="False"/>
                <Setter Property="CanUserAddRows" Value="False"/>
                <Setter Property="CanUserDeleteRows" Value="False"/>
                <Setter Property="CanUserReorderColumns" Value="True"/>
                <Setter Property="CanUserResizeColumns" Value="True"/>
                <Setter Property="CanUserSortColumns" Value="True"/>
                <Setter Property="AlternatingRowBackground" Value="AliceBlue"/>
                <Setter Property="GridLinesVisibility" Value="All"/>
                <Setter Property="HeadersVisibility" Value="All"/>
                <Setter Property="SelectionMode" Value="Extended"/>
                <Setter Property="SelectionUnit" Value="FullRow"/>
                <Setter Property="VirtualizingPanel.IsVirtualizing" Value="True"/>
                <Setter Property="VirtualizingPanel.VirtualizationMode" Value="Recycling"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
